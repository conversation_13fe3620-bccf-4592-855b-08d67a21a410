/*
 * 邮箱申请页面
 * 提供邮箱生成功能，包括不同的生成模式和实时日志显示
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls.Material 2.15
import "../components"

Rectangle {
    id: root
    color: "#f5f5f5"

    // 对外暴露的属性
    property bool isConfigured: false
    property string currentDomain: "未配置"
    property var statistics: ({})
    property var availableTags: []  // 新增：可用标签列表

    // 对外暴露的信号
    signal statusChanged(string message)
    signal logMessage(string message)
    signal requestTagRefresh()  // 新增：请求刷新标签列表
    signal createNewTag(string tagName)  // 新增：创建新标签

    RowLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20

        // 左侧配置信息面板
        Rectangle {
            Layout.preferredWidth: 250
            Layout.fillHeight: true
            color: "white"
            radius: 8
            border.color: "#e0e0e0"

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15

                // 域名信息
                Column {
                    Layout.fillWidth: true
                    spacing: 8

                    Label {
                        text: "📍 当前域名"
                        font.bold: true
                        font.pixelSize: 16
                        color: "#333"
                    }

                    Label {
                        id: domainLabel
                        text: root.currentDomain
                        font.pixelSize: 14
                        color: root.isConfigured ? "#4CAF50" : "#F44336"
                        wrapMode: Text.WordWrap
                        width: parent.width
                    }
                }

                Rectangle {
                    Layout.fillWidth: true
                    height: 1
                    color: "#e0e0e0"
                }

                // 统计信息
                Column {
                    Layout.fillWidth: true
                    spacing: 8

                    Label {
                        text: "📊 统计信息"
                        font.bold: true
                        font.pixelSize: 16
                        color: "#333"
                    }

                    Label {
                        text: "总邮箱数: " + (root.statistics.total_emails || 0)
                        font.pixelSize: 14
                        color: "#666"
                    }

                    Label {
                        text: "今日创建: " + (root.statistics.today_created || 0)
                        font.pixelSize: 14
                        color: "#666"
                    }

                    Label {
                        text: "活跃状态: " + (root.statistics.active_emails || 0)
                        font.pixelSize: 14
                        color: "#666"
                    }

                    Label {
                        text: "成功率: " + (root.statistics.success_rate || 100) + "%"
                        font.pixelSize: 14
                        color: "#666"
                    }
                }

                Item { Layout.fillHeight: true }
            }
        }
        
        // 中央操作区域 - 增加宽度以容纳标签选择器
        Rectangle {
            Layout.preferredWidth: 400
            Layout.fillHeight: true
            color: "white"
            radius: 8
            border.color: "#e0e0e0"

            ScrollView {
                anchors.fill: parent
                anchors.margins: 20
                clip: true
                
                ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
                ScrollBar.vertical.policy: ScrollBar.AsNeeded
                
                ColumnLayout {
                    width: parent.width - 40  // 减去margins
                    spacing: 20

                    Label {
                        text: "🎯 邮箱生成"
                        font.bold: true
                        font.pixelSize: 18
                        color: "#333"
                        Layout.alignment: Qt.AlignHCenter
                    }

                    // 生成模式选择
                    GroupBox {
                        Layout.fillWidth: true
                        title: "生成模式"
                        font.pixelSize: 14
                        
                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 8

                            ButtonGroup {
                                id: prefixTypeGroup
                            }

                            RadioButton {
                                id: randomNameRadio
                                text: "随机名字 (例: john.smith)"
                                checked: true
                                ButtonGroup.group: prefixTypeGroup
                                font.pixelSize: 13
                            }

                            RadioButton {
                                id: randomStringRadio
                                text: "随机字符串 (例: ak7m2x9p)"
                                ButtonGroup.group: prefixTypeGroup
                                font.pixelSize: 13
                            }

                            RadioButton {
                                id: customPrefixRadio
                                text: "自定义前缀"
                                ButtonGroup.group: prefixTypeGroup
                                font.pixelSize: 13
                            }
                        }
                    }

                    // 自定义前缀输入 - 优化样式
                    Rectangle {
                        Layout.fillWidth: true
                        height: 44
                        color: customPrefixRadio.checked ? "white" : "#f8f9fa"
                        radius: 6
                        border.color: customPrefixField.activeFocus ? "#2196F3" : "#e0e0e0"
                        border.width: customPrefixField.activeFocus ? 2 : 1
                        visible: customPrefixRadio.checked
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 12
                            spacing: 8
                            
                            Text {
                                text: "📝"
                                font.pixelSize: 14
                                color: "#666"
                            }
                            
                            TextField {
                                id: customPrefixField
                                Layout.fillWidth: true
                                placeholderText: "输入自定义前缀..."
                                enabled: customPrefixRadio.checked
                                font.pixelSize: 14
                                background: Item {}
                                selectByMouse: true
                            }
                        }
                    }

                    // 标签选择器 - 新功能
                    GroupBox {
                        Layout.fillWidth: true
                        title: "标签设置"
                        font.pixelSize: 14
                        
                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 12
                            
                            Label {
                                text: "为生成的邮箱添加标签，便于分类管理"
                                font.pixelSize: 12
                                color: "#666"
                                wrapMode: Text.WordWrap
                                Layout.fillWidth: true
                            }
                            
                            TagSelector {
                                id: tagSelector
                                Layout.fillWidth: true
                                Layout.preferredHeight: 120
                                maxHeight: 150
                                
                                availableTags: root.availableTags
                                placeholderText: "搜索标签或输入新标签名称..."
                                allowCreateNew: true
                                
                                onNewTagRequested: function(tagName) {
                                    console.log("请求创建新标签:", tagName)
                                    root.createNewTag(tagName)
                                }
                                
                                onTagsChanged: function(selectedTags) {
                                    console.log("选中的标签已变更:", selectedTags.length)
                                }
                            }
                            
                            // 标签管理快捷操作
                            RowLayout {
                                Layout.fillWidth: true
                                spacing: 8
                                
                                Button {
                                    text: "🔄 刷新标签"
                                    font.pixelSize: 11
                                    implicitHeight: 28
                                    flat: true
                                    onClicked: root.requestTagRefresh()
                                    ToolTip.text: "刷新可用标签列表"
                                }
                                
                                Item { Layout.fillWidth: true }
                                
                                Label {
                                    text: "已选择 " + tagSelector.selectedTags.length + " 个标签"
                                    font.pixelSize: 11
                                    color: "#666"
                                }
                            }
                        }
                    }

                    // 备注输入 - 优化样式
                    GroupBox {
                        Layout.fillWidth: true
                        title: "备注信息"
                        font.pixelSize: 14
                        
                        Rectangle {
                            anchors.fill: parent
                            color: "white"
                            radius: 6
                            border.color: notesField.activeFocus ? "#2196F3" : "#e0e0e0"
                            border.width: notesField.activeFocus ? 2 : 1
                            
                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 12
                                spacing: 8
                                
                                Text {
                                    text: "💭"
                                    font.pixelSize: 14
                                    color: "#666"
                                }
                                
                                TextField {
                                    id: notesField
                                    Layout.fillWidth: true
                                    placeholderText: "为邮箱添加备注说明（可选）..."
                                    font.pixelSize: 14
                                    background: Item {}
                                    selectByMouse: true
                                }
                            }
                        }
                    }

                    // 批量生成选项 - 重新设计
                    GroupBox {
                        Layout.fillWidth: true
                        title: "生成选项"
                        font.pixelSize: 14
                        
                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 12
                            
                            CheckBox {
                                id: batchModeCheckBox
                                text: "批量生成模式"
                                font.pixelSize: 13
                                
                                onCheckedChanged: {
                                    if (checked) {
                                        batchCountSpinBox.focus = true
                                    }
                                }
                            }
                            
                            Rectangle {
                                Layout.fillWidth: true
                                height: 44
                                color: batchModeCheckBox.checked ? "white" : "#f8f9fa"
                                radius: 6
                                border.color: batchModeCheckBox.checked ? "#e0e0e0" : "transparent"
                                border.width: 1
                                visible: batchModeCheckBox.checked
                                
                                RowLayout {
                                    anchors.fill: parent
                                    anchors.margins: 12
                                    spacing: 12
                                    
                                    Text {
                                        text: "🔢"
                                        font.pixelSize: 14
                                        color: "#666"
                                    }
                                    
                                    Label {
                                        text: "生成数量:"
                                        font.pixelSize: 13
                                        color: "#333"
                                    }
                                    
                                    SpinBox {
                                        id: batchCountSpinBox
                                        from: 1
                                        to: 50
                                        value: 5
                                        enabled: batchModeCheckBox.checked
                                        implicitWidth: 100
                                        
                                        // 自定义外观
                                        background: Rectangle {
                                            color: "#f8f9fa"
                                            radius: 4
                                            border.color: "#e0e0e0"
                                        }
                                    }
                                    
                                    Label {
                                        text: "个邮箱"
                                        font.pixelSize: 13
                                        color: "#666"
                                    }
                                    
                                    Item { Layout.fillWidth: true }
                                }
                            }
                            
                            // 批量模式提示
                            Rectangle {
                                Layout.fillWidth: true
                                height: 32
                                color: "#fff3e0"
                                radius: 6
                                border.color: "#ffcc02"
                                border.width: 1
                                visible: batchModeCheckBox.checked
                                
                                RowLayout {
                                    anchors.fill: parent
                                    anchors.margins: 8
                                    spacing: 8
                                    
                                    Text {
                                        text: "💡"
                                        font.pixelSize: 12
                                        color: "#f57c00"
                                    }
                                    
                                    Label {
                                        Layout.fillWidth: true
                                        text: "批量模式将同时生成多个邮箱，请注意域名配额限制"
                                        font.pixelSize: 11
                                        color: "#f57c00"
                                        wrapMode: Text.WordWrap
                                    }
                                }
                            }
                        }
                    }

                    // 生成按钮 - 重新设计
                    Rectangle {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 60
                        color: generateButton.enabled ? "#2196F3" : "#e0e0e0"
                        radius: 8
                        
                        // 渐变效果
                        gradient: Gradient {
                            GradientStop { position: 0.0; color: generateButton.enabled ? "#42A5F5" : "#e0e0e0" }
                            GradientStop { position: 1.0; color: generateButton.enabled ? "#1976D2" : "#bdbdbd" }
                        }
                        
                        // 阴影效果
                        Rectangle {
                            anchors.fill: parent
                            anchors.margins: -4
                            color: "#40000000"
                            radius: parent.radius + 4
                            z: -1
                            y: 2
                            visible: generateButton.enabled
                        }
                        
                        Button {
                            id: generateButton
                            anchors.fill: parent
                            text: {
                                if (isGenerating) {
                                    return "🔄 生成中..."
                                } else if (batchModeCheckBox.checked) {
                                    return "🎯 批量生成 " + batchCountSpinBox.value + " 个邮箱"
                                } else {
                                    return "🎯 生成新邮箱"
                                }
                            }
                            font.pixelSize: 16
                            font.weight: Font.Medium
                            enabled: root.isConfigured && !isGenerating
                            
                            background: Rectangle {
                                color: "transparent"
                            }
                            
                            contentItem: Text {
                                text: parent.text
                                font: parent.font
                                color: parent.enabled ? "white" : "#999"
                                horizontalAlignment: Text.AlignHCenter
                                verticalAlignment: Text.AlignVCenter
                            }

                            property bool isGenerating: false

                    // 安全定时器 - 防止按钮永久禁用
                    Timer {
                        id: safetyTimer
                        interval: 30000  // 30秒超时
                        running: generateButton.isGenerating
                        repeat: false
                        onTriggered: {
                            if (generateButton.isGenerating) {
                                console.log("安全定时器触发：重置生成按钮状态")
                                generateButton.isGenerating = false
                                addLogMessage("⚠️ 生成操作超时，已重置按钮状态")
                            }
                        }
                    }

                            onClicked: {
                                if (!validateInput()) {
                                    return
                                }

                                isGenerating = true
                                safetyTimer.restart()

                                var prefixType = "random_name"
                                if (randomStringRadio.checked) prefixType = "random_string"
                                else if (customPrefixRadio.checked) prefixType = "custom"

                                // 获取选中的标签
                                var selectedTagIds = tagSelector.getSelectedTagIds()
                                var selectedTagNames = tagSelector.getSelectedTagNames()
                                
                                console.log("生成邮箱 - 选中标签:", selectedTagNames)

                                // 调用控制器方法
                                if (emailController) {
                                    try {
                                        if (batchModeCheckBox.checked) {
                                            // 批量生成
                                            addLogMessage("🔄 开始批量生成 " + batchCountSpinBox.value + " 个邮箱...")
                                            if (selectedTagNames.length > 0) {
                                                addLogMessage("📌 标签: " + selectedTagNames.join(", "))
                                            }
                                            emailController.batchGenerateEmails(
                                                batchCountSpinBox.value,
                                                prefixType,
                                                customPrefixField.text,
                                                selectedTagIds,  // 传递标签ID数组
                                                notesField.text
                                            )
                                        } else {
                                            // 单个生成
                                            addLogMessage("🔄 开始生成邮箱...")
                                            if (selectedTagNames.length > 0) {
                                                addLogMessage("📌 标签: " + selectedTagNames.join(", "))
                                            }
                                            emailController.generateCustomEmail(
                                                prefixType,
                                                customPrefixField.text,
                                                selectedTagIds,  // 传递标签ID数组
                                                notesField.text
                                            )
                                        }
                                    } catch (e) {
                                        console.error("生成邮箱时发生错误:", e)
                                        addLogMessage("❌ 生成邮箱时发生错误: " + e)
                                        isGenerating = false
                                    }
                                } else {
                                    console.error("emailController未初始化")
                                    addLogMessage("❌ 系统错误: 控制器未初始化")
                                    isGenerating = false
                                }
                            }
                }

                // 进度条
                ProgressBar {
                    id: progressBar
                    Layout.fillWidth: true
                    value: 0
                    visible: value > 0
                }

                // 快速操作区域
                Rectangle {
                    Layout.fillWidth: true
                    height: 80
                    color: "#f8f9fa"
                    radius: 6
                    border.color: "#e9ecef"
                    visible: !generateButton.isGenerating

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 10
                        spacing: 8

                        Label {
                            text: "⚡ 快速操作"
                            font.pixelSize: 12
                            font.bold: true
                            color: "#666"
                        }

                        RowLayout {
                            Layout.fillWidth: true
                            spacing: 5

                            Button {
                                text: "📧 测试邮箱"
                                font.pixelSize: 10
                                implicitHeight: 28
                                Layout.fillWidth: true
                                enabled: latestEmailLabel.text !== ""
                                onClicked: {
                                    if (emailController && latestEmailLabel.text) {
                                        emailController.getVerificationCode(latestEmailLabel.text)
                                    }
                                }
                            }

                            Button {
                                text: "📋 复制"
                                font.pixelSize: 10
                                implicitHeight: 28
                                Layout.fillWidth: true
                                enabled: latestEmailLabel.text !== ""
                                onClicked: {
                                    // 复制到剪贴板
                                    console.log("复制邮箱:", latestEmailLabel.text)
                                    root.logMessage("📋 邮箱地址已复制")
                                }
                            }

                            Button {
                                text: "🔄 重置"
                                font.pixelSize: 10
                                implicitHeight: 28
                                Layout.fillWidth: true
                                onClicked: clearInputs()
                            }
                        }
                    }
                }

                Item { Layout.fillHeight: true }
            }
        }
        
        // 右侧结果和日志区域
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "white"
            radius: 8
            border.color: "#e0e0e0"

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15

                Label {
                    text: "📝 操作日志"
                    font.bold: true
                    font.pixelSize: 16
                    color: "#333"
                }

                // 最新生成的邮箱显示
                Rectangle {
                    id: latestEmailCard
                    Layout.fillWidth: true
                    height: 80
                    color: "#f8f9fa"
                    radius: 6
                    border.color: "#e9ecef"
                    visible: latestEmailLabel.text !== ""

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 15

                        Label {
                            text: "✅ 最新生成的邮箱:"
                            font.pixelSize: 12
                            color: "#666"
                        }

                        Label {
                            id: latestEmailLabel
                            text: ""
                            font.pixelSize: 14
                            font.bold: true
                            color: "#2196F3"

                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    // 复制到剪贴板的功能
                                    console.log("复制邮箱地址:", latestEmailLabel.text)
                                    root.logMessage("📋 邮箱地址已复制到剪贴板")
                                }
                            }
                        }
                    }
                }

                // 日志区域
                ScrollView {
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    TextArea {
                        id: logArea
                        readOnly: true
                        wrapMode: TextArea.Wrap
                        font.family: "Consolas, Monaco, monospace"
                        font.pixelSize: 12
                        color: "#333"
                        text: "[" + new Date().toLocaleTimeString() + "] 邮箱生成页面已加载\n[" + new Date().toLocaleTimeString() + "] 等待用户操作..."

                        function addLog(message) {
                            var timestamp = new Date().toLocaleTimeString()
                            text += "\n[" + timestamp + "] " + message
                            // 自动滚动到底部
                            cursorPosition = length
                        }
                    }
                }
            }
        }
    }
    
    Component.onCompleted: {
        console.log("邮箱生成页面已初始化")
        // 请求刷新标签列表
        root.requestTagRefresh()
        addLogMessage("🔄 正在加载标签列表...")
    }

    // 内部方法
    function updateLatestEmail(email) {
        latestEmailLabel.text = email
    }

    function updateProgress(value) {
        progressBar.value = value / 100.0
    }

    function addLogMessage(message) {
        logArea.addLog(message)
    }

    function enableGenerateButton() {
        generateButton.isGenerating = false
        // 停止安全定时器
        safetyTimer.stop()
        addLogMessage("✅ 生成操作完成，按钮已重新启用")
    }

    function disableGenerateButton() {
        generateButton.isGenerating = true
        addLogMessage("🔒 生成按钮已禁用")
    }
    
    // 标签相关方法
    function refreshTags() {
        console.log("刷新标签列表")
        root.requestTagRefresh()
        addLogMessage("🔄 正在刷新标签列表...")
    }
    
    function handleNewTag(tagName) {
        console.log("处理新标签创建请求:", tagName)
        root.createNewTag(tagName)
        addLogMessage("📝 正在创建新标签: " + tagName)
    }
    
    function onTagCreated(tag) {
        console.log("新标签已创建:", tag.name)
        addLogMessage("✅ 标签创建成功: " + tag.name)
        // 自动选择新创建的标签
        if (tagSelector) {
            tagSelector.selectTagById(tag.id)
        }
    }
    
    function onTagsLoaded(tags) {
        console.log("标签列表已加载，数量:", tags.length)
        root.availableTags = tags
        addLogMessage("✅ 标签列表已加载，共 " + tags.length + " 个标签")
    }

    // 批量生成结果处理
    function handleBatchResult(result) {
        if (result.success > 0) {
            addLogMessage("✅ 批量生成成功: " + result.success + " 个邮箱")
            if (result.emails && result.emails.length > 0) {
                updateLatestEmail(result.emails[0].email_address)
            }
        }
        if (result.failed > 0) {
            addLogMessage("❌ 生成失败: " + result.failed + " 个邮箱")
        }
        if (result.errors && result.errors.length > 0) {
            result.errors.forEach(function(error) {
                addLogMessage("❌ 错误: " + error)
            })
        }
    }

    // 处理批量生成状态更新
    function handleBatchProgress(current, total, message) {
        var percentage = Math.round((current / total) * 100)
        updateProgress(percentage)
        addLogMessage("📊 进度: " + current + "/" + total + " (" + percentage + "%) - " + message)
    }

    // 验证输入
    function validateInput() {
        if (!root.isConfigured) {
            addLogMessage("❌ 请先完成域名配置")
            return false
        }

        if (customPrefixRadio.checked && customPrefixField.text.trim().length === 0) {
            addLogMessage("❌ 请输入自定义前缀")
            return false
        }

        return true
    }

    // 清空输入字段
    function clearInputs() {
        customPrefixField.text = ""
        notesField.text = ""
        randomNameRadio.checked = true
        batchModeCheckBox.checked = false
        if (tagSelector) {
            tagSelector.clearSelection()
        }
        addLogMessage("🧹 输入字段已清空")
    }
}
