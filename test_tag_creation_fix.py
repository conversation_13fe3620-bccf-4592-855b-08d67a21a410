"""
标签创建修复验证脚本

验证标签创建功能的修复情况
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_qml_syntax():
    """测试QML文件语法"""
    print("🔍 检查QML文件语法...")
    
    tag_page_path = Path("src/views/qml/pages/TagManagementPage.qml")
    main_qml_path = Path("src/views/qml/main.qml")
    
    # 检查文件是否存在
    if not tag_page_path.exists():
        print("❌ TagManagementPage.qml 文件不存在")
        return False
    
    if not main_qml_path.exists():
        print("❌ main.qml 文件不存在")
        return False
    
    # 检查TagManagementPage.qml的关键修复
    with open(tag_page_path, 'r', encoding='utf-8') as f:
        tag_content = f.read()
    
    # 验证修复内容
    fixes_to_check = [
        # 响应式设计
        ("响应式设计属性", "property bool isMobile"),
        ("响应式宽度", "root.isMobile ? Math.min(parent.width * 0.95, 450) : 580"),
        
        # 筛选功能
        ("筛选属性", "property var filteredTagList"),
        ("筛选状态", "property bool isFiltered"),
        ("筛选模型", "model: root.isFiltered ? root.filteredTagList : root.tagList"),
        
        # 输入优化
        ("搜索框优化", "placeholderText: activeFocus ? \"\" : \"搜索标签名称、描述、颜色、图标...\""),
        ("浮动标签优化", "visible: !createNameField.activeFocus || createNameField.text.length > 0"),
        
        # 验证函数
        ("内部验证函数", "function validateCreateFormInternal()"),
        ("错误显示函数", "function showValidationError(message)"),
        
        # 颜色验证
        ("颜色验证器", "RegularExpressionValidator"),
        ("颜色格式检查", "var colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/"),
    ]
    
    missing_fixes = []
    for fix_name, fix_content in fixes_to_check:
        if fix_content not in tag_content:
            missing_fixes.append(fix_name)
    
    if missing_fixes:
        print(f"❌ 缺少以下修复内容: {', '.join(missing_fixes)}")
        return False
    
    # 检查main.qml的TagController集成
    with open(main_qml_path, 'r', encoding='utf-8') as f:
        main_content = f.read()
    
    main_fixes = [
        ("TagController集成", "tagController"),
        ("标签创建调用", "tagController.createTag"),
        ("标签列表获取", "tagController.getAllTags"),
        ("信号连接", "target: tagController"),
        ("标签创建信号", "onTagCreated"),
    ]
    
    missing_main_fixes = []
    for fix_name, fix_content in main_fixes:
        if fix_content not in main_content:
            missing_main_fixes.append(fix_name)
    
    if missing_main_fixes:
        print(f"❌ main.qml缺少以下修复内容: {', '.join(missing_main_fixes)}")
        return False
    
    print("✅ QML文件语法检查通过")
    return True

def test_backend_integration():
    """测试后端集成"""
    print("🔍 检查后端集成...")
    
    try:
        # 测试TagController导入
        from controllers.tag_controller import TagController
        from services.database_service import DatabaseService
        
        # 创建测试实例
        db_service = DatabaseService(":memory:")
        # 初始化数据库表
        db_service.init_database()
        tag_controller = TagController(db_service)
        
        print("✅ TagController创建成功")
        
        # 测试标签创建
        import json
        tag_data = {
            "name": "测试标签",
            "description": "测试描述",
            "color": "#FF5722",
            "icon": "🧪"
        }
        
        result_json = tag_controller.createTag(json.dumps(tag_data))
        result = json.loads(result_json)
        
        if result.get("success"):
            print("✅ 标签创建功能正常")
        else:
            print(f"❌ 标签创建失败: {result.get('message')}")
            return False
        
        # 测试标签列表获取
        list_result_json = tag_controller.getAllTags()
        list_result = json.loads(list_result_json)
        
        if list_result.get("success"):
            print("✅ 标签列表获取功能正常")
        else:
            print(f"❌ 标签列表获取失败: {list_result.get('message')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 后端集成测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("🔍 检查文件结构...")
    
    required_files = [
        "src/controllers/tag_controller.py",
        "src/views/qml/pages/TagManagementPage.qml",
        "src/views/qml/main.qml",
        "src/views/modern_main_window.py",
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 文件结构检查通过")
    return True

def main():
    """主测试函数"""
    print("🧪 标签创建修复验证")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("QML语法", test_qml_syntax),
        ("后端集成", test_backend_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！标签创建功能修复成功！")
        print("\n📝 修复总结:")
        print("1. ✅ 修复了validateCreateForm函数作用域问题")
        print("2. ✅ 增加了创建对话框的宽度和高度")
        print("3. ✅ 优化了输入框的文字提示体验")
        print("4. ✅ 重构了筛选功能，支持多维度搜索")
        print("5. ✅ 添加了响应式设计支持")
        print("6. ✅ 集成了真正的TagController后端")
        print("7. ✅ 优化了颜色和图标选择器")
        return True
    else:
        print("❌ 部分测试失败，请检查修复内容")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
