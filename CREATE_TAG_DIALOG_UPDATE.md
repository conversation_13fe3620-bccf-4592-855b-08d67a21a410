测试创建标签对话框的新功能

## 🎯 完成的改进

1. **创建了独立的创建标签对话框组件** (`CreateTagDialog.qml`)
   - 简洁清晰的界面布局
   - 合理的空间分配
   - 支持表情符号和自定义图片两种图标类型

2. **重构了界面结构**
   - 使用 GroupBox 分组管理不同功能区域
   - 响应式设计，适配不同屏幕尺寸
   - 清晰的视觉层次

3. **优化了用户体验**
   - 实时预览效果
   - 表单验证
   - 直观的颜色和图标选择

## 🚀 新对话框特性

### 界面布局
- **预览区域**: 实时显示标签效果
- **基本信息**: 名称和描述输入
- **图标选择**: 表情符号 vs 自定义图片切换
- **颜色选择**: 预设颜色快速选择

### 交互体验
- 清晰的分组布局，避免混乱
- 合理的空间利用，不再重叠
- 流畅的切换动画

## 📝 使用方法

1. 在标签管理页面点击"创建"按钮
2. 新的简洁对话框将打开
3. 填写标签信息并选择图标和颜色
4. 点击"创建标签"完成

## 🔧 安装依赖

如果遇到 PIL 模块导入错误，运行：

```bash
# 在项目目录下运行
./install_custom_icons.sh

# 或手动安装
pip install Pillow==10.4.0
```

## 📋 待办事项

- [ ] 删除旧的复杂对话框代码（需要谨慎操作）
- [ ] 测试新对话框的所有功能
- [ ] 确保与后端服务的正确集成

新的创建标签对话框现在应该能正常工作，界面简洁且功能完整！