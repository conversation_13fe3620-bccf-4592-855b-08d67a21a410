#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI修复测试运行脚本
运行所有UI修复相关的测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from tests.test_ui_fixes import run_ui_fix_tests


def main():
    """主函数"""
    print("=" * 60)
    print("域名邮箱管理器 - UI修复测试")
    print("=" * 60)
    
    # 运行UI修复测试
    success = run_ui_fix_tests()
    
    # 返回结果
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
