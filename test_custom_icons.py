#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义图标功能测试脚本
测试图片上传、处理和存储功能
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加项目路径到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from services.image_service import ImageService
from services.database_service import DatabaseService
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QObject


def create_test_image():
    """创建一个测试图片文件"""
    try:
        from PIL import Image, ImageDraw
        
        # 创建一个简单的测试图片
        img = Image.new('RGBA', (128, 128), (100, 150, 200, 255))
        draw = ImageDraw.Draw(img)
        
        # 绘制一个简单的图形
        draw.rectangle([20, 20, 108, 108], fill=(255, 100, 100, 255))
        draw.ellipse([40, 40, 88, 88], fill=(255, 255, 255, 255))
        
        # 保存到临时文件
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
            img.save(f.name, 'PNG')
            return f.name
            
    except ImportError:
        print("PIL/Pillow 未安装，无法创建测试图片")
        return None


def test_image_service():
    """测试图片服务功能"""
    print("🧪 开始测试自定义图标功能...")
    
    # 创建 Qt 应用（ImageService 需要 Qt 环境）
    app = QApplication(sys.argv)
    
    try:
        # 初始化图片服务
        image_service = ImageService()
        
        print("✅ ImageService 初始化成功")
        
        # 创建测试图片
        test_image_path = create_test_image()
        if not test_image_path:
            print("❌ 无法创建测试图片")
            return
            
        print(f"📷 测试图片已创建: {test_image_path}")
        
        # 测试图片验证
        print("\n🔍 测试图片验证...")
        validation = image_service.validate_image(test_image_path)
        if validation["valid"]:
            print("✅ 图片验证通过")
            print(f"   尺寸: {validation['info']['width']}x{validation['info']['height']}")
            print(f"   格式: {validation['info']['format']}")
            print(f"   大小: {validation['info']['size_mb']}MB")
        else:
            print(f"❌ 图片验证失败: {validation['error']}")
            return
        
        # 测试图片处理
        print("\n🔄 测试图片处理...")
        result = image_service.process_icon_image(test_image_path, "test_tag")
        if result:
            print("✅ 图片处理成功")
            print(f"   处理后路径: {result['relative_path']}")
            print(f"   文件名: {result['filename']}")
            print(f"   最终尺寸: {result['processed_info']['final_size']}")
            
            # 测试 URL 生成
            icon_url = image_service.get_icon_url(result['relative_path'])
            print(f"   图标URL: {icon_url}")
        else:
            print("❌ 图片处理失败")
            return
        
        # 测试存储信息获取
        print("\n📊 测试存储信息...")
        storage_info = image_service.get_storage_info()
        print(f"✅ 存储信息: {storage_info['icons_count']} 个图标, "
              f"{storage_info['total_size_mb']}MB")
        
        # 清理测试文件
        try:
            os.unlink(test_image_path)
            print(f"🧹 已清理测试图片: {test_image_path}")
        except:
            pass
            
        print("\n🎉 所有测试通过！自定义图标功能正常工作。")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()


def test_database_integration():
    """测试数据库集成"""
    print("\n🗄️ 测试数据库集成...")
    
    try:
        # 这里可以添加数据库相关的测试
        # 例如测试标签存储时包含图标路径等
        print("✅ 数据库集成测试占位符（需要完整的数据库环境）")
        
    except Exception as e:
        print(f"❌ 数据库集成测试失败: {e}")


def main():
    """主函数"""
    print("🏷️ 自定义图标功能测试套件")
    print("=" * 50)
    
    # 检查依赖
    try:
        import PIL
        print(f"✅ PIL/Pillow 已安装 (版本: {PIL.__version__})")
    except ImportError:
        print("❌ 缺少依赖: PIL/Pillow")
        print("请运行: pip install Pillow")
        return
    
    # 运行测试
    test_image_service()
    test_database_integration()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")


if __name__ == "__main__":
    main()